import Mathlib.Data.Real.Basic
import Mathlib.Data.Real.Sqrt
import Mathlib.Algebra.Order.Field.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.Linarith
import Mathlib.Tactic.NormNum

/-!
# Problem: Find positive number a such that 6, a, b and 1/b, a, 54 are geometric progressions

This file proves that a = 3√2 is the unique positive solution.
-/

open Real

-- Main theorem statement
theorem geometric_progressions_solution :
  ∃! a : ℝ, a > 0 ∧ ∃ b : ℝ, b > 0 ∧
    (∃ r₁ : ℝ, a = 6 * r₁ ∧ b = a * r₁) ∧  -- 6, a, b is geometric progression
    (∃ r₂ : ℝ, a = (1/b) * r₂ ∧ 54 = a * r₂) -- 1/b, a, 54 is geometric progression
  := by
  -- The unique solution is a = 3√2
  use 3 * sqrt 2
  constructor
  · -- Prove that a = 3√2 is a solution
    constructor
    · -- Prove a > 0
      apply mul_pos
      · norm_num
      · exact sqrt_pos.mpr (by norm_num)
    · -- Prove existence of b and the geometric progression conditions
      use 3  -- b = 3
      constructor
      · norm_num  -- b > 0
      · constructor
        · -- First geometric progression: 6, a, b
          use sqrt 2 / 2  -- r₁ = √2/2
          constructor
          · -- a = 6 * r₁
            -- We need to show 3 * sqrt 2 = 6 * (sqrt 2 / 2)
            ring
          · -- b = a * r₁
            -- We need to show 3 = (3 * sqrt 2) * (sqrt 2 / 2)
            -- This simplifies to 3 = 3 * sqrt 2 * sqrt 2 / 2 = 3 * 2 / 2 = 3
            calc 3
              = 3 * 2 / 2 := by norm_num
              _ = 3 * (sqrt 2 * sqrt 2) / 2 := by
                have h : (2 : ℝ) = sqrt 2 * sqrt 2 := (mul_self_sqrt (by norm_num : (0 : ℝ) ≤ 2)).symm
                rw [h]
              _ = 3 * sqrt 2 * (sqrt 2 / 2) := by ring
        · -- Second geometric progression: 1/b, a, 54
          use 9 * sqrt 2  -- r₂ = 9√2
          constructor
          · -- a = (1/b) * r₂
            sorry
          · -- 54 = a * r₂
            sorry
  · -- Prove uniqueness
    intro a' ha'
    -- Extract conditions from ha'
    obtain ⟨ha'_pos, b', hb'_pos, ⟨r₁', hr₁'_a, hr₁'_b⟩, ⟨r₂', hr₂'_a, hr₂'_54⟩⟩ := ha'

    -- Use geometric mean property: middle term squared equals product of neighbors
    have h1 : a' ^ 2 = 6 * b' := by
      -- From geometric progression 6, a', b'
      -- We have r₁' such that a' = 6 * r₁' and b' = a' * r₁'
      -- From b' = a' * r₁' and a' = 6 * r₁', we get b' = (6 * r₁') * r₁' = 6 * r₁'^2
      -- From a' = 6 * r₁', we get r₁' = a' / 6
      -- So b' = 6 * (a' / 6)^2 = 6 * a'^2 / 36 = a'^2 / 6
      -- Therefore a'^2 = 6 * b'
      have h_r1_eq : r₁' = a' / 6 := by
        rw [hr₁'_a]
        field_simp
      have h_b_eq : b' = a' * r₁' := hr₁'_b
      rw [h_b_eq, h_r1_eq]
      field_simp
      ring

    have h2 : a' ^ 2 = 54 / b' := by
      -- From geometric progression 1/b', a', 54
      -- We have r₂' such that a' = (1/b') * r₂' and 54 = a' * r₂'
      -- From 54 = a' * r₂' and a' = (1/b') * r₂', we get 54 = ((1/b') * r₂') * r₂' = (1/b') * r₂'^2
      -- From a' = (1/b') * r₂', we get r₂' = a' * b'
      -- So 54 = (1/b') * (a' * b')^2 = (1/b') * a'^2 * b'^2 = a'^2 * b'
      -- Therefore a'^2 = 54 / b'
      have h_r2_eq : r₂' = a' * b' := by
        rw [hr₂'_a]
        field_simp
      have h_54_eq : 54 = a' * r₂' := hr₂'_54
      rw [h_54_eq, h_r2_eq]
      field_simp
      ring

    -- Equate the two expressions for a'^2
    have h3 : 6 * b' = 54 / b' := by
      rw [← h1, ← h2]

    -- Solve for b'
    have hb' : b' = 3 := by
      -- From 6 * b' = 54 / b', we get 6 * b'^2 = 54, so b'^2 = 9, thus b' = 3
      have hb'_ne_zero : b' ≠ 0 := ne_of_gt hb'_pos
      -- Multiply both sides of h3 by b'
      have h_mult_both_sides : 6 * b' * b' = 54 := by
        have h_eq : (6 * b') * b' = (54 / b') * b' := by rw [h3]
        rw [div_mul_cancel₀ 54 hb'_ne_zero] at h_eq
        exact h_eq
      -- Rewrite as 6 * b'^2 = 54
      have h_six_b_sq : 6 * b' ^ 2 = 54 := by
        rw [pow_two]
        rw [mul_assoc] at h_mult_both_sides
        exact h_mult_both_sides
      -- So b'^2 = 9
      have h_b_sq : b' ^ 2 = 9 := by
        linarith [h_six_b_sq]
      -- Since b' > 0, we have b' = 3
      have h_b_eq_sqrt_9 : b' = sqrt 9 := by
        rw [← Real.sqrt_sq (le_of_lt hb'_pos), h_b_sq]
      rw [h_b_eq_sqrt_9]
      -- √9 = √(3²) = 3 since 3 ≥ 0
      have h_nine_eq_three_sq : (9 : ℝ) = 3 ^ 2 := by norm_num
      rw [h_nine_eq_three_sq, Real.sqrt_sq (by norm_num : (0 : ℝ) ≤ 3)]

    -- Solve for a'
    have ha' : a' = 3 * sqrt 2 := by
      -- From a'^2 = 6 * b' = 6 * 3 = 18, we get a' = √18 = 3√2
      -- Substitute b' = 3 into h1: a'^2 = 6 * b'
      have h_a_sq_18 : a' ^ 2 = 18 := by
        rw [h1, hb']
        norm_num
      -- Since a' > 0, we have a' = √18
      have h_a_eq_sqrt_18 : a' = sqrt 18 := by
        rw [← Real.sqrt_sq (le_of_lt ha'_pos), h_a_sq_18]
      rw [h_a_eq_sqrt_18]
      -- Simplify √18 = √(9 * 2) = √9 * √2 = 3√2
      have h_18_eq_9_times_2 : (18 : ℝ) = 9 * 2 := by norm_num
      rw [h_18_eq_9_times_2, Real.sqrt_mul (by norm_num : (0 : ℝ) ≤ 9)]
      have h_sqrt_9_eq_3 : sqrt 9 = 3 := by
        rw [← Real.sqrt_sq (by norm_num : (0 : ℝ) ≤ 3)]
        norm_num
      rw [h_sqrt_9_eq_3]

    exact ha'

-- Alternative proof using geometric mean property directly
theorem geometric_progressions_solution_geom_mean :
  ∃! a : ℝ, a > 0 ∧ ∃ b : ℝ, b > 0 ∧
    (a ^ 2 = 6 * b) ∧  -- Geometric mean property for first progression
    (a ^ 2 = 54 / b)   -- Geometric mean property for second progression
  := by
  use 3 * sqrt 2
  constructor
  · -- Prove that a = 3√2 is a solution
    constructor
    · -- a > 0
      apply mul_pos
      · norm_num
      · exact sqrt_pos.mpr (by norm_num)
    · -- Existence of b and conditions
      use 3
      constructor
      · norm_num  -- b > 0
      · constructor
        · -- a² = 6b
          sorry
        · -- a² = 54/b
          sorry
  · -- Uniqueness
    intro a' ha'
    obtain ⟨ha'_pos, b', hb'_pos, h_eq1, h_eq2⟩ := ha'

    -- From a'^2 = 6*b' and a'^2 = 54/b', we get 6*b' = 54/b'
    have h_eq : 6 * b' = 54 / b' := by
      rw [← h_eq1, ← h_eq2]

    -- Solve for b': 6*b'^2 = 54, so b'^2 = 9, thus b' = 3
    have hb' : b' = 3 := by
      sorry

    -- Solve for a': a'^2 = 6*3 = 18, so a' = √18 = 3√2
    have ha' : a' = 3 * sqrt 2 := by
      sorry

    exact ha'
